{"c": ["app/(dashboard)/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@radix-ui/react-avatar/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-separator/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs", "(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/audio-waveform.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/badge-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-up-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/command.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gallery-vertical-end.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-terminal.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_check_private_redeclaration.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_apply_descriptor_get.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_apply_descriptor_set.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_extract_field_descriptor.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_private_field_get.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_private_field_init.js", "(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_class_private_field_set.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/shim/index.js", "(app-pages-browser)/./src/components/AppSidebar.tsx", "(app-pages-browser)/./src/components/NavMain.tsx", "(app-pages-browser)/./src/components/NavUser.tsx", "(app-pages-browser)/./src/components/team-switcher.tsx", "(app-pages-browser)/./src/components/ui/avatar.tsx", "(app-pages-browser)/./src/components/ui/collapsible.tsx", "(app-pages-browser)/./src/components/ui/dropdown-menu.tsx", "(app-pages-browser)/./src/components/ui/input.tsx", "(app-pages-browser)/./src/components/ui/separator.tsx", "(app-pages-browser)/./src/components/ui/sheet.tsx", "(app-pages-browser)/./src/components/ui/sidebar.tsx", "(app-pages-browser)/./src/components/ui/skeleton.tsx", "(app-pages-browser)/./src/hooks/use-mobile.tsx"]}