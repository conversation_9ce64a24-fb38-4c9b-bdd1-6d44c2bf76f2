"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@remix-run";
exports.ids = ["vendor-chunks/@remix-run"];
exports.modules = {

/***/ "(ssr)/./node_modules/@remix-run/router/dist/router.js":
/*!*******************************************************!*\
  !*** ./node_modules/@remix-run/router/dist/router.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortedDeferredError: () => (/* binding */ AbortedDeferredError),\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   IDLE_BLOCKER: () => (/* binding */ IDLE_BLOCKER),\n/* harmony export */   IDLE_FETCHER: () => (/* binding */ IDLE_FETCHER),\n/* harmony export */   IDLE_NAVIGATION: () => (/* binding */ IDLE_NAVIGATION),\n/* harmony export */   UNSAFE_DEFERRED_SYMBOL: () => (/* binding */ UNSAFE_DEFERRED_SYMBOL),\n/* harmony export */   UNSAFE_DeferredData: () => (/* binding */ DeferredData),\n/* harmony export */   UNSAFE_ErrorResponseImpl: () => (/* binding */ ErrorResponseImpl),\n/* harmony export */   UNSAFE_convertRouteMatchToUiMatch: () => (/* binding */ convertRouteMatchToUiMatch),\n/* harmony export */   UNSAFE_convertRoutesToDataRoutes: () => (/* binding */ convertRoutesToDataRoutes),\n/* harmony export */   UNSAFE_decodePath: () => (/* binding */ decodePath),\n/* harmony export */   UNSAFE_getResolveToMatches: () => (/* binding */ getResolveToMatches),\n/* harmony export */   UNSAFE_invariant: () => (/* binding */ invariant),\n/* harmony export */   UNSAFE_warning: () => (/* binding */ warning),\n/* harmony export */   createBrowserHistory: () => (/* binding */ createBrowserHistory),\n/* harmony export */   createHashHistory: () => (/* binding */ createHashHistory),\n/* harmony export */   createMemoryHistory: () => (/* binding */ createMemoryHistory),\n/* harmony export */   createPath: () => (/* binding */ createPath),\n/* harmony export */   createRouter: () => (/* binding */ createRouter),\n/* harmony export */   createStaticHandler: () => (/* binding */ createStaticHandler),\n/* harmony export */   data: () => (/* binding */ data),\n/* harmony export */   defer: () => (/* binding */ defer),\n/* harmony export */   generatePath: () => (/* binding */ generatePath),\n/* harmony export */   getStaticContextFromError: () => (/* binding */ getStaticContextFromError),\n/* harmony export */   getToPathname: () => (/* binding */ getToPathname),\n/* harmony export */   isDataWithResponseInit: () => (/* binding */ isDataWithResponseInit),\n/* harmony export */   isDeferredData: () => (/* binding */ isDeferredData),\n/* harmony export */   isRouteErrorResponse: () => (/* binding */ isRouteErrorResponse),\n/* harmony export */   joinPaths: () => (/* binding */ joinPaths),\n/* harmony export */   json: () => (/* binding */ json),\n/* harmony export */   matchPath: () => (/* binding */ matchPath),\n/* harmony export */   matchRoutes: () => (/* binding */ matchRoutes),\n/* harmony export */   normalizePathname: () => (/* binding */ normalizePathname),\n/* harmony export */   parsePath: () => (/* binding */ parsePath),\n/* harmony export */   redirect: () => (/* binding */ redirect),\n/* harmony export */   redirectDocument: () => (/* binding */ redirectDocument),\n/* harmony export */   replace: () => (/* binding */ replace),\n/* harmony export */   resolvePath: () => (/* binding */ resolvePath),\n/* harmony export */   resolveTo: () => (/* binding */ resolveTo),\n/* harmony export */   stripBasename: () => (/* binding */ stripBasename)\n/* harmony export */ });\n/**\n * @remix-run/router v1.23.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  function createHref(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 1\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 0\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  let index = getIndex();\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || {\n      idx: null\n    };\n    return state.idx;\n  }\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({\n        action,\n        location: history.location,\n        delta\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 1\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 0\n      });\n    }\n  }\n  function createURL(to) {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n    return new URL(href, base);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n}\n//#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nconst immutableRouteKeys = new Set([\"lazy\", \"caseSensitive\", \"path\", \"id\", \"index\", \"children\"]);\nfunction isIndexRoute(route) {\n  return route.index === true;\n}\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nfunction convertRoutesToDataRoutes(routes, mapRouteProperties, parentPath, manifest) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (manifest === void 0) {\n    manifest = {};\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!manifest[id], \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, mapRouteProperties(route), {\n        id\n      });\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, mapRouteProperties(route), {\n        id,\n        children: undefined\n      });\n      manifest[id] = pathOrLayoutRoute;\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(route.children, mapRouteProperties, treePath, manifest);\n      }\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/v6/utils/match-routes\n */\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\nfunction matchRoutesImpl(routes, locationArg, basename, allowPartial) {\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch(branches[i], decoded, allowPartial);\n  }\n  return matches;\n}\nfunction convertRouteMatchToUiMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    var _route$path;\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments;\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = [];\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\")));\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname, allowPartial) {\n  if (allowPartial === void 0) {\n    allowPartial = false;\n  }\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    let route = meta.route;\n    if (!match && end && allowPartial && !routesMeta[routesMeta.length - 1].route.index) {\n      match = matchPath({\n        path: meta.relativePath,\n        caseSensitive: meta.caseSensitive,\n        end: false\n      }, remainingPathname);\n    }\n    if (!match) {\n      return null;\n    }\n    Object.assign(matchedParams, match.params);\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/v6/utils/generate-path\n */\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n  const stringify = p => p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n  const segments = path.split(/\\/+/).map((segment, index, array) => {\n    const isLastSegment = index === array.length - 1;\n    // only apply the splat if it's the last segment\n    if (isLastSegment && segment === \"*\") {\n      const star = \"*\";\n      // Apply the splat\n      return stringify(params[star]);\n    }\n    const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n    if (keyMatch) {\n      const [, key, optional] = keyMatch;\n      let param = params[key];\n      invariant(optional === \"?\" || param != null, \"Missing \\\":\" + key + \"\\\" param\");\n      return stringify(param);\n    }\n    // Remove any optional markers from optional static segments\n    return segment.replace(/\\?$/g, \"\");\n  })\n  // Remove empty segments\n  .filter(segment => !!segment);\n  return prefix + segments.join(\"/\");\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/v6/utils/match-path\n */\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, compiledParams] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = compiledParams.reduce((memo, _ref, index) => {\n    let {\n      paramName,\n      isOptional\n    } = _ref;\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    const value = captureGroups[index];\n    if (isOptional && !value) {\n      memo[paramName] = undefined;\n    } else {\n      memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n    }\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let params = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:([\\w-]+)(\\?)?/g, (_, paramName, isOptional) => {\n    params.push({\n      paramName,\n      isOptional: isOptional != null\n    });\n    return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    params.push({\n      paramName: \"*\"\n    });\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, params];\n}\nfunction decodePath(value) {\n  try {\n    return value.split(\"/\").map(v => decodeURIComponent(v).replace(/\\//g, \"%2F\")).join(\"/\");\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/v6/utils/resolve-path\n */\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nfunction getResolveToMatches(matches, v7_relativeSplatPath) {\n  let pathMatches = getPathContributingMatches(matches);\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) => idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase);\n  }\n  return pathMatches.map(match => match.pathnameBase);\n}\n/**\n * @private\n */\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from;\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    }\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from);\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated The `json` method is deprecated in favor of returning raw objects.\n * This method will be removed in v7.\n */\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass DataWithResponseInit {\n  constructor(data, init) {\n    this.type = \"DataWithResponseInit\";\n    this.data = data;\n    this.init = init || null;\n  }\n}\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nfunction data(data, init) {\n  return new DataWithResponseInit(data, typeof init === \"number\" ? {\n    status: init\n  } : init);\n}\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data, responseInit) {\n    this.pendingKeysSet = new Set();\n    this.subscribers = new Set();\n    this.deferredKeys = [];\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\");\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n    this.init = responseInit;\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, undefined, data), error => this.onSettle(promise, key, error));\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeysSet.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\"Deferred data for key \\\"\" + key + \"\\\" resolved/rejected with `undefined`, \" + \"you must resolve/reject with a value or `null`.\");\n      Object.defineProperty(promise, \"_error\", {\n        get: () => undefinedError\n      });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    this.emit(false, key);\n    return data;\n  }\n  emit(aborted, settledKey) {\n    this.subscribers.forEach(subscriber => subscriber(aborted, settledKey));\n  }\n  subscribe(fn) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref3) => {\n      let [key, value] = _ref3;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n/**\n * @deprecated The `defer` method is deprecated in favor of returning raw\n * objects. This method will be removed in v7.\n */\nconst defer = function defer(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  return new DeferredData(data, responseInit);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirectDocument = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst replace = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nclass ErrorResponseImpl {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\n\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined\n};\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nconst defaultMapRouteProperties = route => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary)\n});\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Create a router and listen to history POP navigations\n */\nfunction createRouter(init) {\n  const routerWindow = init.window ? init.window : typeof window !== \"undefined\" ? window : undefined;\n  const isBrowser = typeof routerWindow !== \"undefined\" && typeof routerWindow.document !== \"undefined\" && typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let mapRouteProperties;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Routes keyed by ID\n  let manifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(init.routes, mapRouteProperties, undefined, manifest);\n  let inFlightDataRoutes;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n  // Config driven behavior flags\n  let future = _extends({\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false\n  }, init.future);\n  // Cleanup function for history\n  let unlistenHistory = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors = null;\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(initialMatches, dataRoutes, init.history.location.pathname);\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n  let initialized;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(null, dataRoutes, init.history.location.pathname);\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some(m => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some(m => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(m => errors[m.route.id] !== undefined);\n      initialized = initialMatches.slice(0, idx + 1).every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(m => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map()\n  };\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction = Action.Pop;\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n  // AbortController for the active navigation\n  let pendingNavigationController;\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions = new Map();\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener = null;\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes = [];\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads = new Set();\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map();\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map();\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set();\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map();\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map();\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set();\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map();\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map();\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate = undefined;\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location,\n        delta\n      } = _ref;\n      // Ignore this event if it was just us resetting the URL from a\n      // blocked POP navigation\n      if (unblockBlockerHistoryUpdate) {\n        unblockBlockerHistoryUpdate();\n        unblockBlockerHistoryUpdate = undefined;\n        return;\n      }\n      warning(blockerFunctions.size === 0 || delta != null, \"You are trying to use a blocker on a POP navigation to a location \" + \"that was not created by @remix-run/router. This will fail silently in \" + \"production. This can happen if you are navigating outside the router \" + \"via `window.history.pushState`/`window.location.hash` instead of using \" + \"router navigation APIs.  This can also happen if you are using \" + \"createHashRouter and the user manually changes the URL.\");\n      let blockerKey = shouldBlockNavigation({\n        currentLocation: state.location,\n        nextLocation: location,\n        historyAction\n      });\n      if (blockerKey && delta != null) {\n        // Restore the URL to match the current UI, but don't update router state\n        let nextHistoryUpdatePromise = new Promise(resolve => {\n          unblockBlockerHistoryUpdate = resolve;\n        });\n        init.history.go(delta * -1);\n        // Put the blocker into a blocked state\n        updateBlocker(blockerKey, {\n          state: \"blocked\",\n          location,\n          proceed() {\n            updateBlocker(blockerKey, {\n              state: \"proceeding\",\n              proceed: undefined,\n              reset: undefined,\n              location\n            });\n            // Re-do the same POP navigation we just blocked, after the url\n            // restoration is also complete.  See:\n            // https://github.com/remix-run/react-router/issues/11613\n            nextHistoryUpdatePromise.then(() => init.history.go(delta));\n          },\n          reset() {\n            let blockers = new Map(state.blockers);\n            blockers.set(blockerKey, IDLE_BLOCKER);\n            updateState({\n              blockers\n            });\n          }\n        });\n        return;\n      }\n      return startNavigation(historyAction, location);\n    });\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () => persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () => routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location, {\n        initialHydration: true\n      });\n    }\n    return router;\n  }\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n  // Subscribe to state updates for the router\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n  // Update our state and notify the calling context of the change\n  function updateState(newState, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state = _extends({}, state, newState);\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers = [];\n    let deletedFetchersKeys = [];\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n    // Remove any lingering deleted fetchers that have already been removed\n    // from state.fetchers\n    deletedFetchers.forEach(key => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        deletedFetchersKeys.push(key);\n      }\n    });\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach(subscriber => subscriber(state, {\n      deletedFetchers: deletedFetchersKeys,\n      viewTransitionOpts: opts.viewTransitionOpts,\n      flushSync: opts.flushSync === true\n    }));\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach(key => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach(key => deleteFetcher(key));\n    } else {\n      // We already called deleteFetcher() on these, can remove them from this\n      // Set now that we've handed the keys off to the data layer\n      deletedFetchersKeys.forEach(key => deletedFetchers.delete(key));\n    }\n  }\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(location, newState, _temp) {\n    var _location$state, _location$state2;\n    let {\n      flushSync\n    } = _temp === void 0 ? {} : _temp;\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData;\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && ((_location$state2 = location.state) == null ? void 0 : _location$state2._isRedirect) !== true;\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n    if (isUninterruptedRevalidation) ; else if (pendingAction === Action.Pop) ; else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    }\n    let viewTransitionOpts;\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === Action.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location\n      };\n    }\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset,\n      blockers\n    }), {\n      viewTransitionOpts,\n      flushSync: flushSync === true\n    });\n    // Reset stateful navigation vars\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, to, future.v7_relativeSplatPath, opts == null ? void 0 : opts.fromRouteId, opts == null ? void 0 : opts.relative);\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, false, normalizedPath, opts);\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = _extends({}, nextLocation, init.history.encodeLocation(nextLocation));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ; else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    let flushSync = (opts && opts.flushSync) === true;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey, IDLE_BLOCKER);\n          updateState({\n            blockers\n          });\n        }\n      });\n      return;\n    }\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync\n    });\n  }\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    });\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    }\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation,\n      // Proxy through any rending view transition\n      enableViewTransition: pendingViewTransitionEnabled === true\n    });\n  }\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true;\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = opts != null && opts.initialHydration && state.matches && state.matches.length > 0 && !initialMatchesIsFOW ?\n    // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n    state.matches : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (matches && state.initialized && !isRevalidationRequired && isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, {\n        matches\n      }, {\n        flushSync\n      });\n      return;\n    }\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let {\n        error,\n        notFoundMatches,\n        route\n      } = handleNavigational404(location.pathname);\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      }, {\n        flushSync\n      });\n      return;\n    }\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(init.history, location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionResult;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [findNearestBoundary(matches).route.id, {\n        type: ResultType.error,\n        error: opts.pendingError\n      }];\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(request, location, opts.submission, matches, fogOfWar.active, {\n        replace: opts.replace,\n        flushSync\n      });\n      if (actionResult.shortCircuited) {\n        return;\n      }\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (isErrorResult(result) && isRouteErrorResponse(result.error) && result.error.status === 404) {\n          pendingNavigationController = null;\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error\n            }\n          });\n          return;\n        }\n      }\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n      // Create a GET request for the loaders\n      request = createClientSideRequest(init.history, request.url, request.signal);\n    }\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, fogOfWar.active, loadingNavigation, opts && opts.submission, opts && opts.fetcherSubmission, opts && opts.replace, opts && opts.initialHydration === true, flushSync, pendingActionResult);\n    if (shortCircuited) {\n      return;\n    }\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches: updatedMatches || matches\n    }, getActionDataForCommit(pendingActionResult), {\n      loaderData,\n      errors\n    }));\n  }\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(request, location, submission, matches, isFogOfWar, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    interruptActiveLoads();\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({\n      navigation\n    }, {\n      flushSync: opts.flushSync === true\n    });\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [boundaryId, {\n            type: ResultType.error,\n            error: discoverResult.error\n          }]\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          notFoundMatches,\n          error,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [route.id, {\n            type: ResultType.error,\n            error\n          }]\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    // Call our action and get the result\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", state, request, [actionMatch], matches, null);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(result.response.headers.get(\"Location\"), new URL(request.url), basename);\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result]\n      };\n    }\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result]\n    };\n  }\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(request, location, matches, isFogOfWar, overrideNavigation, submission, fetcherSubmission, replace, initialHydration, flushSync, pendingActionResult) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation || getLoadingNavigation(location, submission);\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission = submission || fetcherSubmission || getSubmissionFromNavigation(loadingNavigation);\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState = !isUninterruptedRevalidation && (!future.v7_partialHydration || !initialHydration);\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(_extends({\n          navigation: loadingNavigation\n        }, actionData !== undefined ? {\n          actionData\n        } : {}), {\n          flushSync\n        });\n      }\n      let discoverResult = await discoverRoutes(matches, location.pathname, request.signal);\n      if (discoverResult.type === \"aborted\") {\n        return {\n          shortCircuited: true\n        };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches).route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error\n          }\n        };\n      } else if (!discoverResult.matches) {\n        let {\n          error,\n          notFoundMatches,\n          route\n        } = handleNavigational404(location.pathname);\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error\n          }\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, activeSubmission, location, future.v7_partialHydration && initialHydration === true, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult);\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId));\n    pendingNavigationLoadId = ++incrementingLoadId;\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null\n      }, getActionDataForCommit(pendingActionResult), updatedFetchers ? {\n        fetchers: new Map(state.fetchers)\n      } : {}), {\n        flushSync\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (shouldUpdateNavigationState) {\n      let updates = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, {\n        flushSync\n      });\n    }\n    revalidatingFetchers.forEach(rf => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(f => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    }\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    revalidatingFetchers.forEach(rf => fetchControllers.delete(rf.key));\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = _extends({}, state.errors, errors);\n    }\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers = updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n    return _extends({\n      matches,\n      loaderData,\n      errors\n    }, shouldUpdateFetchers ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getUpdatedActionData(pendingActionResult) {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n  function getUpdatedRevalidatingFetchers(revalidatingFetchers) {\n    revalidatingFetchers.forEach(rf => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(undefined, fetcher ? fetcher.data : undefined);\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    abortFetcher(key);\n    let flushSync = (opts && opts.flushSync) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, href, future.v7_relativeSplatPath, routeId, opts == null ? void 0 : opts.relative);\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: normalizedPath\n      }), {\n        flushSync\n      });\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, true, normalizedPath, opts);\n    if (error) {\n      setFetcherError(key, routeId, error, {\n        flushSync\n      });\n      return;\n    }\n    let match = getTargetMatch(matches, path);\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n      return;\n    }\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, {\n      routeId,\n      path\n    });\n    handleFetcherLoader(key, routeId, path, match, matches, fogOfWar.active, flushSync, preventScrollReset, submission);\n  }\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    function detectAndHandle405Error(m) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId\n        });\n        setFetcherError(key, routeId, error, {\n          flushSync\n        });\n        return true;\n      }\n      return false;\n    }\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal, submission);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(requestMatches, new URL(fetchRequest.url).pathname, fetchRequest.signal, key);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\"action\", state, fetchRequest, [match], requestMatches, key);\n    let actionResult = actionResults[match.route.id];\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset\n          });\n        }\n      }\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(init.history, nextLocation, abortController.signal);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(routesToUse, state.navigation.location, basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, submission, nextLocation, false, future.v7_skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, [match.route.id, actionResult]);\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers.filter(rf => rf.key !== key).forEach(rf => {\n      let staleKey = rf.key;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = getLoadingFetcher(undefined, existingFetcher ? existingFetcher.data : undefined);\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      abortFetcher(staleKey);\n      if (rf.controller) {\n        fetchControllers.set(staleKey, rf.controller);\n      }\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(rf => abortFetcher(rf.key));\n    abortController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    let {\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    abortController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(r => fetchControllers.delete(r.key));\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(revalidationRequest, redirect.result, false, {\n        preventScrollReset\n      });\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n    abortStaleFetchLoads(loadId);\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors),\n        fetchers: new Map(state.fetchers)\n      });\n      isRevalidationRequired = false;\n    }\n  }\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(key, routeId, path, match, matches, isFogOfWar, flushSync, preventScrollReset, submission) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getLoadingFetcher(submission, existingFetcher ? existingFetcher.data : undefined), {\n      flushSync\n    });\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal);\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(matches, new URL(fetchRequest.url).pathname, fetchRequest.signal, key);\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, {\n          flushSync\n        });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(key, routeId, getInternalRouterError(404, {\n          pathname: path\n        }), {\n          flushSync\n        });\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\"loader\", state, fetchRequest, [match], matches, key);\n    let result = results[match.route.id];\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    }\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset\n        });\n        return;\n      }\n    }\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(request, redirect, isNavigation, _temp2) {\n    let {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace\n    } = _temp2 === void 0 ? {} : _temp2;\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(location, new URL(request.url), basename);\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true\n    });\n    if (isBrowser) {\n      let isDocumentReload = false;\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n        // Hard reload if it's an absolute URL to a new origin\n        url.origin !== routerWindow.location.origin ||\n        // Hard reload if it's an absolute URL that does not match our basename\n        stripBasename(url.pathname, basename) == null;\n      }\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true || redirect.response.headers.has(\"X-Remix-Replace\") ? Action.Replace : Action.Push;\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let {\n      formMethod,\n      formAction,\n      formEncType\n    } = state.navigation;\n    if (!submission && !fetcherSubmission && formMethod && formAction && formEncType) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (redirectPreserveMethodStatusCodes.has(redirect.response.status) && activeSubmission && isMutationMethod(activeSubmission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, activeSubmission, {\n          formAction: location\n        }),\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(redirectLocation, submission);\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation ? pendingViewTransitionEnabled : undefined\n      });\n    }\n  }\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, state, request, matchesToLoad, matches, fetcherKey) {\n    let results;\n    let dataResults = {};\n    try {\n      results = await callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties);\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach(m => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e\n        };\n      });\n      return dataResults;\n    }\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, future.v7_relativeSplatPath)\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(result);\n      }\n    }\n    return dataResults;\n  }\n  async function callLoadersAndMaybeResolveData(state, matches, matchesToLoad, fetchersToLoad, request) {\n    let currentMatches = state.matches;\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\"loader\", state, request, matchesToLoad, matches, null);\n    let fetcherResultsPromise = Promise.all(fetchersToLoad.map(async f => {\n      if (f.matches && f.match && f.controller) {\n        let results = await callDataStrategy(\"loader\", state, createClientSideRequest(init.history, f.path, f.controller.signal), [f.match], f.matches, f.key);\n        let result = results[f.match.route.id];\n        // Fetcher results are keyed by fetcher key from here on out, not routeId\n        return {\n          [f.key]: result\n        };\n      } else {\n        return Promise.resolve({\n          [f.key]: {\n            type: ResultType.error,\n            error: getInternalRouterError(404, {\n              pathname: f.path\n            })\n          }\n        });\n      }\n    }));\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce((acc, r) => Object.assign(acc, r), {});\n    await Promise.all([resolveNavigationDeferredResults(matches, loaderResults, request.signal, currentMatches, state.loaderData), resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad)]);\n    return {\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n  function updateFetcherState(key, fetcher, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function setFetcherError(key, routeId, error, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    }, {\n      flushSync: (opts && opts.flushSync) === true\n    });\n  }\n  function getFetcher(key) {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    // If this fetcher was previously marked for deletion, unmark it since we\n    // have a new instance\n    if (deletedFetchers.has(key)) {\n      deletedFetchers.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n  function deleteFetcher(key) {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (fetchControllers.has(key) && !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    // If we opted into the flag we can clear this now since we're calling\n    // deleteFetcher() at the end of updateState() and we've already handed the\n    // deleted fetcher keys off to the data layer.\n    // If not, we're eagerly calling deleteFetcher() and we need to keep this\n    // Set populated until the next updateState call, and we'll clear\n    // `deletedFetchers` then\n    if (future.v7_fetcherPersist) {\n      deletedFetchers.delete(key);\n    }\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n  function deleteFetcherAndUpdateState(key) {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      deletedFetchers.add(key);\n      if (!future.v7_fetcherPersist) {\n        deleteFetcher(key);\n      }\n    } else {\n      activeFetchers.set(key, count);\n    }\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\", \"Invalid blocker state transition: \" + blocker.state + \" -> \" + newBlocker.state);\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({\n      blockers\n    });\n  }\n  function shouldBlockNavigation(_ref2) {\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = _ref2;\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({\n      currentLocation,\n      nextLocation,\n      historyAction\n    })) {\n      return blockerKey;\n    }\n  }\n  function handleNavigational404(pathname) {\n    let error = getInternalRouterError(404, {\n      pathname\n    });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(routesToUse);\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n    return {\n      notFoundMatches: matches,\n      route,\n      error\n    };\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function getScrollKey(location, matches) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(location, matches.map(m => convertRouteMatchToUiMatch(m, state.loaderData)));\n      return key || location.key;\n    }\n    return location.key;\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  function checkFogOfWar(matches, routesToUse, pathname) {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n        return {\n          active: true,\n          matches: fogMatches || []\n        };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n          return {\n            active: true,\n            matches: partialMatches\n          };\n        }\n      }\n    }\n    return {\n      active: false,\n      matches: null\n    };\n  }\n  async function discoverRoutes(matches, pathname, signal, fetcherKey) {\n    if (!patchRoutesOnNavigationImpl) {\n      return {\n        type: \"success\",\n        matches\n      };\n    }\n    let partialMatches = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          fetcherKey,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(routeId, children, routesToUse, localManifest, mapRouteProperties);\n          }\n        });\n      } catch (e) {\n        return {\n          type: \"error\",\n          error: e,\n          partialMatches\n        };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n      if (signal.aborted) {\n        return {\n          type: \"aborted\"\n        };\n      }\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return {\n          type: \"success\",\n          matches: newMatches\n        };\n      }\n      let newPartialMatches = matchRoutesImpl(routesToUse, pathname, basename, true);\n      // Avoid loops if the second pass results in the same partial matches\n      if (!newPartialMatches || partialMatches.length === newPartialMatches.length && partialMatches.every((m, i) => m.route.id === newPartialMatches[i].route.id)) {\n        return {\n          type: \"success\",\n          matches: null\n        };\n      }\n      partialMatches = newPartialMatches;\n    }\n  }\n  function _internalSetRoutes(newRoutes) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(newRoutes, mapRouteProperties, undefined, manifest);\n  }\n  function patchRoutes(routeId, children) {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties);\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes\n  };\n  return router;\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\nconst UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let manifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties;\n  if (opts != null && opts.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts != null && opts.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future = _extends({\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false\n  }, opts ? opts.future : null);\n  let dataRoutes = convertRoutesToDataRoutes(routes, mapRouteProperties, undefined, manifest);\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(request, _temp3) {\n    let {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, skipLoaderErrorBubbling === true, null);\n    if (isResponse(result)) {\n      return result;\n    }\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(request, _temp4) {\n    let {\n      routeId,\n      requestContext,\n      dataStrategy\n    } = _temp4 === void 0 ? {} : _temp4;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let result = await queryImpl(request, location, matches, requestContext, dataStrategy || null, false, match);\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      var _result$activeDeferre;\n      let data = Object.values(result.loaderData)[0];\n      if ((_result$activeDeferre = result.activeDeferreds) != null && _result$activeDeferre[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n    return undefined;\n  }\n  async function queryImpl(request, location, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, dataStrategy, skipLoaderErrorBubbling, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      let results = await callDataStrategy(\"action\", request, [actionMatch], matches, isRouteRequest, requestContext, dataStrategy);\n      result = results[actionMatch.route.id];\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling ? actionMatch : findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null, [boundaryMatch.route.id, result]);\n      // action status codes take precedence over loader status codes\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : result.statusCode != null ? result.statusCode : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    }\n    let context = await loadRouteData(loaderRequest, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, null);\n    return _extends({}, context, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      }\n    }, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionHeaders: result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {}\n    });\n  }\n  async function loadRouteData(request, matches, requestContext, dataStrategy, skipLoaderErrorBubbling, routeMatch, pendingActionResult) {\n    let isRouteRequest = routeMatch != null;\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader) && !(routeMatch != null && routeMatch.route.lazy)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n    let requestMatches = routeMatch ? [routeMatch] : pendingActionResult && isErrorResult(pendingActionResult[1]) ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]) : matches;\n    let matchesToLoad = requestMatches.filter(m => m.route.loader || m.route.lazy);\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionResult && isErrorResult(pendingActionResult[1]) ? {\n          [pendingActionResult[0]]: pendingActionResult[1].error\n        } : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let results = await callDataStrategy(\"loader\", request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy);\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n    // Process and commit output from loaders\n    let activeDeferreds = new Map();\n    let context = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling);\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set(matchesToLoad.map(match => match.route.id));\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches,\n      activeDeferreds: activeDeferreds.size > 0 ? Object.fromEntries(activeDeferreds.entries()) : null\n    });\n  }\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(type, request, matchesToLoad, matches, isRouteRequest, requestContext, dataStrategy) {\n    let results = await callDataStrategyImpl(dataStrategy || defaultDataStrategy, type, null, request, matchesToLoad, matches, null, manifest, mapRouteProperties, requestContext);\n    let dataResults = {};\n    await Promise.all(matches.map(async match => {\n      if (!(match.route.id in results)) {\n        return;\n      }\n      let result = results[match.route.id];\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result;\n        // Throw redirects and let the server handle them with an HTTP redirect\n        throw normalizeRelativeRoutingRedirectResponse(response, request, match.route.id, matches, basename, future.v7_relativeSplatPath);\n      }\n      if (isResponse(result.result) && isRouteRequest) {\n        // For SSR single-route requests, we want to hand Responses back\n        // directly without unwrapping\n        throw result;\n      }\n      dataResults[match.route.id] = await convertDataStrategyResultToDataResult(result);\n    }));\n    return dataResults;\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n}\nfunction throwStaticHandlerAbortedError(request, isRouteRequest, future) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(method + \"() call aborted: \" + request.method + \" \" + request.url);\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && (\"formData\" in opts && opts.formData != null || \"body\" in opts && opts.body !== undefined);\n}\nfunction normalizeTo(location, matches, basename, prependBasename, to, v7_relativeSplatPath, fromRouteId, relative) {\n  let contextualMatches;\n  let activeRouteMatch;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n  // Resolve the relative path\n  let path = resolveTo(to ? to : \".\", getResolveToMatches(contextualMatches, v7_relativeSplatPath), stripBasename(location.pathname, basename) || location.pathname, relative === \"path\");\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(normalizeFormMethod, isFetcher, path, opts) {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  }\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, {\n      type: \"invalid-body\"\n    })\n  });\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod ? rawFormMethod.toUpperCase() : rawFormMethod.toLowerCase();\n  let formAction = stripHashFromPath(path);\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      let text = typeof opts.body === \"string\" ? opts.body : opts.body instanceof FormData || opts.body instanceof URLSearchParams ?\n      // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n      Array.from(opts.body.entries()).reduce((acc, _ref3) => {\n        let [name, value] = _ref3;\n        return \"\" + acc + name + \"=\" + value + \"\\n\";\n      }, \"\") : String(opts.body);\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text\n        }\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      try {\n        let json = typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined\n          }\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n  invariant(typeof FormData === \"function\", \"FormData is not available in this environment\");\n  let searchParams;\n  let formData;\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n  let submission = {\n    formMethod,\n    formAction,\n    formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined\n  };\n  if (isMutationMethod(submission.formMethod)) {\n    return {\n      path,\n      submission\n    };\n  }\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = \"?\" + searchParams;\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n}\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId, includeBoundary) {\n  if (includeBoundary === void 0) {\n    includeBoundary = false;\n  }\n  let index = matches.findIndex(m => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\nfunction getMatchesToLoad(history, state, matches, submission, location, initialHydration, skipActionErrorRevalidation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, deletedFetchers, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionResult) {\n  let actionResult = pendingActionResult ? isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : pendingActionResult[1].data : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, Object.keys(state.errors)[0], true);\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(matches, pendingActionResult[0]);\n  }\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult ? pendingActionResult[1].statusCode : undefined;\n  let shouldSkipRevalidation = skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let {\n      route\n    } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (route.loader == null) {\n      return false;\n    }\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n    // Always call the loader on new route instances and pending defer cancellations\n    if (isNewLoader(state.loaderData, state.matches[index], match) || cancelledDeferredRoutes.some(id => id === match.route.id)) {\n      return true;\n    }\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n    return shouldRevalidateLoader(match, _extends({\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params\n    }, submission, {\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation ? false :\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      isRevalidationRequired || currentUrl.pathname + currentUrl.search === nextUrl.pathname + nextUrl.search ||\n      // Search params affect all loaders\n      currentUrl.search !== nextUrl.search || isNewRouteInstance(currentRouteMatch, nextRouteMatch)\n    }));\n  });\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (initialHydration || !matches.some(m => m.route.id === f.routeId) || deletedFetchers.has(key)) {\n      return;\n    }\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null\n      });\n      return;\n    }\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (fetcher && fetcher.state !== \"idle\" && fetcher.data === undefined) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, _extends({\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params\n      }, submission, {\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation ? false : isRevalidationRequired\n      }));\n    }\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController()\n      });\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction shouldLoadRouteOnHydration(route, loaderData, errors) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id;\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\nfunction patchRoutesImpl(routeId, children, routesToUse, manifest, mapRouteProperties) {\n  var _childrenToPatch;\n  let childrenToPatch;\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(route, \"No route found to patch children into: routeId = \" + routeId);\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(newRoute => !childrenToPatch.some(existingRoute => isSameRoute(newRoute, existingRoute)));\n  let newRoutes = convertRoutesToDataRoutes(uniqueChildren, mapRouteProperties, [routeId || \"_\", \"patch\", String(((_childrenToPatch = childrenToPatch) == null ? void 0 : _childrenToPatch.length) || \"0\")], manifest);\n  childrenToPatch.push(...newRoutes);\n}\nfunction isSameRoute(newRoute, existingRoute) {\n  // Most optimal check is by id\n  if (\"id\" in newRoute && \"id\" in existingRoute && newRoute.id === existingRoute.id) {\n    return true;\n  }\n  // Second is by pathing differences\n  if (!(newRoute.index === existingRoute.index && newRoute.path === existingRoute.path && newRoute.caseSensitive === existingRoute.caseSensitive)) {\n    return false;\n  }\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if ((!newRoute.children || newRoute.children.length === 0) && (!existingRoute.children || existingRoute.children.length === 0)) {\n    return true;\n  }\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children.every((aChild, i) => {\n    var _existingRoute$childr;\n    return (_existingRoute$childr = existingRoute.children) == null ? void 0 : _existingRoute$childr.some(bChild => isSameRoute(aChild, bChild));\n  });\n}\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(route, mapRouteProperties, manifest) {\n  if (!route.lazy) {\n    return;\n  }\n  let lazyRoute = await route.lazy();\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue = routeToUpdate[lazyRouteProperty];\n    let isPropertyStaticallyDefined = staticRouteValue !== undefined &&\n    // This property isn't static since it should always be updated based\n    // on the route updates\n    lazyRouteProperty !== \"hasErrorBoundary\";\n    warning(!isPropertyStaticallyDefined, \"Route \\\"\" + routeToUpdate.id + \"\\\" has a static property \\\"\" + lazyRouteProperty + \"\\\" \" + \"defined but its lazy function is also returning a value for this property. \" + (\"The lazy route property \\\"\" + lazyRouteProperty + \"\\\" will be ignored.\"));\n    if (!isPropertyStaticallyDefined && !immutableRouteKeys.has(lazyRouteProperty)) {\n      routeUpdates[lazyRouteProperty] = lazyRoute[lazyRouteProperty];\n    }\n  }\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, _extends({}, mapRouteProperties(routeToUpdate), {\n    lazy: undefined\n  }));\n}\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy(_ref4) {\n  let {\n    matches\n  } = _ref4;\n  let matchesToLoad = matches.filter(m => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map(m => m.resolve()));\n  return results.reduce((acc, result, i) => Object.assign(acc, {\n    [matchesToLoad[i].route.id]: result\n  }), {});\n}\nasync function callDataStrategyImpl(dataStrategyImpl, type, state, request, matchesToLoad, matches, fetcherKey, manifest, mapRouteProperties, requestContext) {\n  let loadRouteDefinitionsPromises = matches.map(m => m.route.lazy ? loadLazyRouteModule(m.route, mapRouteProperties, manifest) : undefined);\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some(m => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve = async handlerOverride => {\n      if (handlerOverride && request.method === \"GET\" && (match.route.lazy || match.route.loader)) {\n        shouldLoad = true;\n      }\n      return shouldLoad ? callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, requestContext) : Promise.resolve({\n        type: ResultType.data,\n        result: undefined\n      });\n    };\n    return _extends({}, match, {\n      shouldLoad,\n      resolve\n    });\n  });\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext\n  });\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n  return results;\n}\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(type, request, match, loadRoutePromise, handlerOverride, staticContext) {\n  let result;\n  let onReject;\n  let runHandler = handler => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise((_, r) => reject = r);\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    let actualHandler = ctx => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(new Error(\"You cannot call the handler for a route which defines a boolean \" + (\"\\\"\" + type + \"\\\" [routeId: \" + match.route.id + \"]\")));\n      }\n      return handler({\n        request,\n        params: match.params,\n        context: staticContext\n      }, ...(ctx !== undefined ? [ctx] : []));\n    };\n    let handlerPromise = (async () => {\n      try {\n        let val = await (handlerOverride ? handlerOverride(ctx => actualHandler(ctx)) : actualHandler());\n        return {\n          type: \"data\",\n          result: val\n        };\n      } catch (e) {\n        return {\n          type: \"error\",\n          result: e\n        };\n      }\n    })();\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n  try {\n    let handler = match.route[type];\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n        // If the handler throws, don't let it immediately bubble out,\n        // since we need to let the lazy() execution finish so we know if this\n        // route has a boundary that can handle the error\n        runHandler(handler).catch(e => {\n          handlerError = e;\n        }), loadRoutePromise]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return {\n            type: ResultType.data,\n            result: undefined\n          };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n    invariant(result.result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return {\n      type: ResultType.error,\n      result: e\n    };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n  return result;\n}\nasync function convertDataStrategyResultToDataResult(dataStrategyResult) {\n  let {\n    result,\n    type\n  } = dataStrategyResult;\n  if (isResponse(result)) {\n    let data;\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      var _result$init3, _result$init4;\n      if (result.data instanceof Error) {\n        var _result$init, _result$init2;\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: (_result$init = result.init) == null ? void 0 : _result$init.status,\n          headers: (_result$init2 = result.init) != null && _result$init2.headers ? new Headers(result.init.headers) : undefined\n        };\n      }\n      // Convert thrown data() to ErrorResponse instances\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(((_result$init3 = result.init) == null ? void 0 : _result$init3.status) || 500, undefined, result.data),\n        statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n        headers: (_result$init4 = result.init) != null && _result$init4.headers ? new Headers(result.init.headers) : undefined\n      };\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined\n    };\n  }\n  if (isDeferredData(result)) {\n    var _result$init5, _result$init6;\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: (_result$init5 = result.init) == null ? void 0 : _result$init5.status,\n      headers: ((_result$init6 = result.init) == null ? void 0 : _result$init6.headers) && new Headers(result.init.headers)\n    };\n  }\n  if (isDataWithResponseInit(result)) {\n    var _result$init7, _result$init8;\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: (_result$init7 = result.init) == null ? void 0 : _result$init7.status,\n      headers: (_result$init8 = result.init) != null && _result$init8.headers ? new Headers(result.init.headers) : undefined\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n}\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(response, request, routeId, matches, basename, v7_relativeSplatPath) {\n  let location = response.headers.get(\"Location\");\n  invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1);\n    location = normalizeTo(new URL(request.url), trimmedMatches, basename, true, location, v7_relativeSplatPath);\n    response.headers.set(\"Location\", location);\n  }\n  return response;\n}\nfunction normalizeRedirectLocation(location, currentUrl, basename) {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\") ? new URL(currentUrl.protocol + normalizedLocation) : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType\n    } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({\n        \"Content-Type\": formEncType\n      });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (formEncType === \"application/x-www-form-urlencoded\" && submission.formData) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n  return searchParams;\n}\nfunction convertSearchParamsToFormData(searchParams) {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\nfunction processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, skipLoaderErrorBubbling) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {};\n  let pendingError = pendingActionResult && isErrorResult(pendingActionResult[1]) ? pendingActionResult[1].error : undefined;\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach(match => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n      errors = errors || {};\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = {\n      [pendingActionResult[0]]: pendingError\n    };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, results, pendingActionResult, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, results, pendingActionResult, activeDeferreds, false // This method is only called client side so we always want to bubble\n  );\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach(rf => {\n    let {\n      key,\n      match,\n      controller\n    } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match == null ? void 0 : match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\nfunction getActionDataForCommit(pendingActionResult) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1]) ? {\n    // Clear out prior actionData on errors\n    actionData: {}\n  } : {\n    actionData: {\n      [pendingActionResult[0]]: pendingActionResult[1].data\n    }\n  };\n}\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.length === 1 ? routes[0] : routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\nfunction getInternalRouterError(status, _temp5) {\n  let {\n    pathname,\n    routeId,\n    method,\n    type,\n    message\n  } = _temp5 === void 0 ? {} : _temp5;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n  return new ErrorResponseImpl(status || 500, statusText, new Error(errorMessage), true);\n}\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results) {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return {\n        key,\n        result\n      };\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\nfunction isDataStrategyResult(result) {\n  return result != null && typeof result === \"object\" && \"type\" in result && \"result\" in result && (result.type === ResultType.data || result.type === ResultType.error);\n}\nfunction isRedirectDataStrategyResultResult(result) {\n  return isResponse(result.result) && redirectStatusCodes.has(result.result.status);\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isDataWithResponseInit(value) {\n  return typeof value === \"object\" && value != null && \"type\" in value && \"data\" in value && \"init\" in value && value.type === \"DataWithResponseInit\";\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method.toLowerCase());\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method.toLowerCase());\n}\nasync function resolveNavigationDeferredResults(matches, results, signal, currentMatches, currentLoaderData) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then(result => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveFetcherDeferredResults(matches, results, revalidatingFetchers) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let {\n      key,\n      routeId,\n      controller\n    } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find(m => (m == null ? void 0 : m.route.id) === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(controller, \"Expected an AbortController for revalidating fetcher deferred result\");\n      await resolveDeferredData(result, controller.signal, true).then(result => {\n        if (result) {\n          results[key] = result;\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\nfunction getSubmissionFromNavigation(navigation) {\n  let {\n    formMethod,\n    formAction,\n    formEncType,\n    text,\n    formData,\n    json\n  } = navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined\n    };\n  }\n}\nfunction getLoadingNavigation(location, submission) {\n  if (submission) {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text\n    };\n    return navigation;\n  } else {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined\n    };\n    return navigation;\n  }\n}\nfunction getSubmittingNavigation(location, submission) {\n  let navigation = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text\n  };\n  return navigation;\n}\nfunction getLoadingFetcher(submission, data) {\n  if (submission) {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data\n    };\n    return fetcher;\n  } else {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data\n    };\n    return fetcher;\n  }\n}\nfunction getSubmittingFetcher(submission, existingFetcher) {\n  let fetcher = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined\n  };\n  return fetcher;\n}\nfunction getDoneFetcher(data) {\n  let fetcher = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data\n  };\n  return fetcher;\n}\nfunction restoreAppliedTransitions(_window, transitions) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(TRANSITIONS_STORAGE_KEY);\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\nfunction persistAppliedTransitions(_window, transitions) {\n  if (transitions.size > 0) {\n    let json = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(TRANSITIONS_STORAGE_KEY, JSON.stringify(json));\n    } catch (error) {\n      warning(false, \"Failed to save applied view transitions in sessionStorage (\" + error + \").\");\n    }\n  }\n}\n//#endregion\n\n\n//# sourceMappingURL=router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@remix-run/router/dist/router.js\n");

/***/ })

};
;