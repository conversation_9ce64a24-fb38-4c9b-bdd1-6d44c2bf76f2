"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/page.tsx":
/*!**********************************!*\
  !*** ./app/(dashboard)/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _components_PaginatedDatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PaginatedDatedTable */ \"(app-pages-browser)/./src/components/PaginatedDatedTable.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst locationData = [\n    {\n        id: 1,\n        lat: 10.0,\n        lng: -10.0,\n        status: 'genuine',\n        location: 'Central America',\n        date: '2025-05-01'\n    },\n    {\n        id: 2,\n        lat: 20.0,\n        lng: -10.0,\n        status: 'tampered',\n        location: 'Caribbean',\n        date: '2025-05-02'\n    },\n    {\n        id: 3,\n        lat: 30.0,\n        lng: -40.0,\n        status: 'genuine',\n        location: 'West Africa',\n        date: '2025-05-03'\n    },\n    {\n        id: 4,\n        lat: 50.0,\n        lng: -20.0,\n        status: 'tampered',\n        location: 'Western Europe',\n        date: '2025-05-04'\n    }\n];\n// Stats cards data\nconst statsCards = [\n    {\n        title: 'Total Scans',\n        value: '14,568',\n        change: '+20.1%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: 'Genuine QR',\n        value: '14,563',\n        change: '+18.1%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: 'Tampered QR',\n        value: '5',\n        change: '+1.9%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 11\n        }, undefined)\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('heatmap');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: statsCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-card text-card-foreground overflow-hidden dark:border-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-2 pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: card.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        card.icon\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: card.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-500\",\n                                                children: card.change\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            ' ',\n                                            card.period\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"QR Scan Locations\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewType === 'heatmap' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewType('heatmap'),\n                                children: \"Map View\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewType === 'list-view' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewType('list-view'),\n                                children: \"List View\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            viewType === 'list-view' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PaginatedDatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                data: locationData,\n                itemsPerPage: 7\n            }, void 0, false, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 rounded-md shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-96 relative\",\n                    id: \"map-container\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"FSxhFenvp8kC8OVdNuX4ONHZ9X8=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/page.tsx\n"));

/***/ })

});