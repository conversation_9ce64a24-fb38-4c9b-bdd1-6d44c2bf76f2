"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/page.tsx":
/*!**********************************!*\
  !*** ./app/(dashboard)/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user-round.js\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CircleDollarSign,CircleUser,CircleUserRound!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _components_PaginatedDatedTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PaginatedDatedTable */ \"(app-pages-browser)/./src/components/PaginatedDatedTable.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst locationData = [\n    {\n        id: 1,\n        lat: 10.0,\n        lng: -10.0,\n        status: 'genuine',\n        location: 'Central America',\n        date: '2025-05-01'\n    },\n    {\n        id: 2,\n        lat: 20.0,\n        lng: -10.0,\n        status: 'tampered',\n        location: 'Caribbean',\n        date: '2025-05-02'\n    },\n    {\n        id: 3,\n        lat: 30.0,\n        lng: -40.0,\n        status: 'genuine',\n        location: 'West Africa',\n        date: '2025-05-03'\n    },\n    {\n        id: 4,\n        lat: 50.0,\n        lng: -20.0,\n        status: 'tampered',\n        location: 'Western Europe',\n        date: '2025-05-04'\n    }\n];\n// Stats cards data\nconst statsCards = [\n    {\n        title: 'Total Scans',\n        value: '14,568',\n        change: '+20.1%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: 'Genuine QR',\n        value: '14,563',\n        change: '+18.1%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: 'Tampered QR',\n        value: '5',\n        change: '+1.9%',\n        period: 'from last month',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleDollarSign_CircleUser_CircleUserRound_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5 text-muted-foreground\"\n        }, void 0, false, {\n            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 11\n        }, undefined)\n    }\n];\nfunction DashboardPage() {\n    _s();\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('heatmap');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: statsCards.map((card, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                className: \"pb-3 pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                            children: card.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gray-100 dark:bg-gray-700 rounded-lg\",\n                                            children: card.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-gray-900 dark:text-white mb-2\",\n                                        children: card.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 dark:text-green-400 font-medium\",\n                                                children: card.change\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            ' ',\n                                            card.period\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: \"QR Scan Locations\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: viewType === 'heatmap' ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setViewType('heatmap'),\n                                            className: \"text-sm\",\n                                            children: \"Map View\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: viewType === 'list-view' ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setViewType('list-view'),\n                                            className: \"text-sm\",\n                                            children: \"List View\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-0\",\n                        children: viewType === 'list-view' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PaginatedDatedTable__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            data: locationData,\n                            itemsPerPage: 7\n                        }, void 0, false, {\n                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-96 relative flex items-center justify-center\",\n                                id: \"map-container\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"World Map\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Map component will be displayed here\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\sentinel_dashboard\\\\app\\\\(dashboard)\\\\page.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"FSxhFenvp8kC8OVdNuX4ONHZ9X8=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/page.tsx\n"));

/***/ })

});